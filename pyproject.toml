[build-system]
requires = [
    "hatchling>=1.24.2",
]
build-backend = "hatchling.build"

[project]
name = "register-api"
description = "API for miner registration and resource allocation/deallocation"
author="neuralinternet.ai"
authors = [
     {name = "Neural Internet"},
]
readme = "README.md"
license = { text = "MIT" }
requires-python = ">=3.10"
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: Developers",
    "Topic :: Software Development :: Build Tools",
    "License :: OSI Approved :: MIT License",
    "Natural Language :: English",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3 :: Only",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",    
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: Implementation :: CPython",
    "Framework :: FastAPI",
    "Environment :: GPU :: NVIDIA CUDA :: 12",
    "Operating System :: POSIX :: Linux",
    "Topic :: Scientific/Engineering",
    "Topic :: Scientific/Engineering :: Mathematics",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development",
    "Topic :: Software Development :: Libraries",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
dynamic = ["version"]

dependencies = [
    "at",
    "bittensor",
    "black",
    "cryptography",
    "docker",
    "google-cloud-pubsub",
    "GPUtil",
    "igpu",
    "numpy",
    "psutil",
    "pyinstaller",
    "wandb",
    "pyfiglet",
    "python-dotenv",
    "requests",
    "paramiko",
    "blake3",
    "ipwhois",
    "torch"
]

[project.optional-dependencies]
dev = [
    "h11==0.14.0",
    "httpcore==1.0.8",  
    "pytest",
    "httpx",
    "pytest-depends",
    "allure-pytest",
    "pip-tools"
]

[project.urls]
Homepage = "https://neuralinternet.ai/"
Sponsor = "https://bittensor.org"
Source = "https://github.com/neuralinternet/register-api"

[tool.hatch.version]
path = "src/__init__.py"

[tool.hatch.build]
allow-direct-references = true

[tool.hatch.metadata]
allow-direct-references = true

[tool.hatch.build.targets.wheel]
packages = [
    "src",
    "libs"
]

[tool.hatch.build.targets.sdist]
exclude = [
    "/.github"
]

[tool.pytest.ini_options]
addopts = "-v --cov=. --cov-report=term-missing"
testpaths = [
    "tests"
]
