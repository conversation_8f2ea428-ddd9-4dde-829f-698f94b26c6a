# Standard Library Imports
import base64
import json
import asyncio
from typing import List, Dict, Any, Optional

# Third-Party Libraries
import torch
import bittensor as bt
from bittensor import Axon, Dendrite

# Custom/Project-Specific Libraries
from compute.protocol import Allocate
import neurons.RSAEncryption as rsa
from neurons.Validator.database.allocate import (
    select_allocate_miners_hotkey,
    get_miner_details,
)
from compute.utils.db import ComputeDb
from src.api.models.register import DeviceRequirement, DockerRequirement


def allocate_best_candidate(
    db: ComputeDb,
    bt_components: Dict[str, Any],
    device_requirement: DeviceRequirement,
    timeline: int,
    public_key: str,
    docker_requirement: Dict[str, Any],
) -> Dict[str, Any]:
    """
    Allocate the container with the given device requirement.
    """
    # Generate ssh connection for given device requirements and timeline

    # Find out the candidates
    candidates_hotkey = select_allocate_miners_hotkey(db, device_requirement)

    axon_candidates = []
    for axon in bt_components["metagraph"].axons:
        if axon.hotkey in candidates_hotkey:
            axon_candidates.append(axon)

    responses = bt_components["dendrite"].query(
        axon_candidates,
        Allocate(
            timeline=timeline, device_requirement=device_requirement, checking=True
        ),
    )

    final_candidates_hotkey = []

    for index, response in enumerate(responses):
        hotkey = axon_candidates[index].hotkey
        if response and response["status"] is True:
            final_candidates_hotkey.append(hotkey)

    # Check if there are candidates
    if len(final_candidates_hotkey) <= 0:
        return {"status": False, "msg": "Requested resource is not available."}

    # Sort the candidates with their score
    scores = torch.ones_like(
        torch.tensor(bt_components["metagraph"].S), dtype=torch.float32
    )

    score_dict = {
        hotkey: score
        for hotkey, score in zip(
            [axon.hotkey for axon in bt_components["metagraph"].axons], scores
        )
    }
    sorted_hotkeys = sorted(
        final_candidates_hotkey,
        key=lambda hotkey: score_dict.get(hotkey, 0),
        reverse=True,
    )
    # Loop the sorted candidates and check if one can allocate the device
    for hotkey in sorted_hotkeys:
        index = bt_components["metagraph"].hotkeys.index(hotkey)
        axon = bt_components["metagraph"].axons[index]
        allocate_params = {
            "timeline": timeline,
            "device_requirement": device_requirement,
            "checking": False,
            "public_key": public_key,
        }

        if docker_requirement is not None:
            allocate_params["docker_requirement"] = docker_requirement
        register_response = bt_components["dendrite"].query(
            axon,
            Allocate(**allocate_params),
            timeout=100,
        )

        if register_response and register_response["status"] is True:
            register_response["ip"] = axon.ip
            register_response["hotkey"] = axon.hotkey
            return register_response

    return {"status": False, "msg": "Requested resource is not available."}


def process_allocation_result(
    db: ComputeDb, result: Dict[str, Any], private_key: str, public_key: str
) -> Dict[str, Any]:
    result_hotkey = result["hotkey"]
    result_info = result["info"]
    private_key = private_key.encode("utf-8")
    decrypted_info_str = rsa.decrypt_data(private_key, base64.b64decode(result_info))
    specs_details = get_miner_details(db)
    for key, details in specs_details.items():
        if str(key) == str(result_hotkey) and details:
            try:
                gpu_miner = details["gpu"]
                gpu_name = str(gpu_miner["details"][0]["name"]).lower()
                break
            except (KeyError, IndexError, TypeError):
                gpu_name = "Invalid details"
            else:
                gpu_name = "No details available"
    info = json.loads(decrypted_info_str)
    info["ip"] = result["ip"]
    info["resource"] = gpu_name
    info["regkey"] = public_key
    info["result_hotkey"] = result_hotkey
    return info


def get_hotkey_list(db: ComputeDb) -> List[str]:
    hotkey_list = []

    # Instantiate the connection to the db
    cursor = db.get_cursor()
    if cursor is None:
        bt.logging.error("Failed to get database cursor.")
        return hotkey_list

    try:
        # Retrieve all records from the allocation table
        cursor.execute("SELECT hotkey FROM allocation")
        rows = cursor.fetchall()

        hotkey_list = [row[0] for row in rows]

    except Exception as e:
        print(f"An error occurred while retrieving allocation details: {e}")
    finally:
        cursor.close()

    return hotkey_list


async def check_and_allocate(
    hotkey: str,
    axon: Axon,
    dendrite: Dendrite,
    timeline: int,
    device_requirement: DeviceRequirement,
    timeout: int,
    public_key: str,
    docker_requirement: Optional[DockerRequirement] = None,
) -> Optional[Dict[str, Any]]:
    try:
        check_allocation = await dendrite(
            axon,
            Allocate(
                timeline=timeline,
                device_requirement=device_requirement,
                checking=True,
            ),
            timeout=timeout,
        )

        bt.logging.info(f"API: Allocation check passed for hotkey: {hotkey}")

        if check_allocation and check_allocation["status"] is True:
            allocate = Allocate(
                timeline=timeline,
                device_requirement=device_requirement,
                checking=False,
                public_key=public_key,
                docker_requirement=docker_requirement if docker_requirement else None,
            )

            register_response = await dendrite(axon, allocate, timeout=timeout)

            if register_response and register_response["status"] is True:
                register_response["ip"] = axon.ip
                register_response["hotkey"] = axon.hotkey
                register_response["miner_version"] = axon.version
                return register_response

        bt.logging.warning(f"API: Allocation check failed for hotkey: {hotkey}")
        return None
    except Exception as e:
        return {"error": f"Error during allocation: {e}"}


async def process_result(
    hotkey: str,
    result: Dict[str, Any],
    specs_details: Dict[str, Any],
    private_key: str,
    public_key: str,
    docker_requirement: Optional[DockerRequirement] = None,
    uuid_key: Optional[str] = None,
) -> Dict[str, Any]:
    for key, details in specs_details.items():
        if str(key) == str(hotkey) and details:
            try:
                gpu_miner = details["gpu"]
                gpu_name = str(gpu_miner["details"][0]["name"]).lower()
                break
            except (KeyError, IndexError, TypeError):
                gpu_name = "Invalid details"
        else:
            gpu_name = "No details available"

    result_info = result["info"]
    private_key = private_key.encode("utf-8")
    decrypted_info_str = rsa.decrypt_data(private_key, base64.b64decode(result_info))

    info = json.loads(decrypted_info_str)
    info["ip"] = result["ip"]
    info["resource"] = gpu_name
    info["regkey"] = public_key
    if docker_requirement is not None:
        info["ssh_key"] = docker_requirement.ssh_key
    if uuid_key is not None:
        info["uuid"] = uuid_key

    return info


def process_result_sync(
    hotkey: str,
    result: Dict[str, Any],
    specs_details: Dict[str, Any],
    private_key: str,
    public_key: str,
) -> Dict[str, Any]:
    return asyncio.run(
        process_result(hotkey, result, specs_details, private_key, public_key)
    )


def get_allocations_from_db(db: ComputeDb, checking_allocated: List[str]) -> List[dict]:
    cursor = db.get_cursor()
    allocation_list = []

    try:
        cursor.execute("SELECT id, hotkey, details FROM allocation")
        rows = cursor.fetchall()

        if not rows:
            return None

        for row in rows:
            id, hotkey, details = row
            info = json.loads(details)
            entry = {
                "id": id,
                "hotkey": hotkey,
                "resource": info.get("resource"),
                "username": info.get("username"),
                "password": info.get("password"),
                "port": info.get("port"),
                "ip": info.get("ip"),
                "ssh_key": info.get("ssh_key"),
                "uuid_key": info.get("uuid"),
                "ssh_command": f"ssh {info.get('username')}@{info.get('ip')} -p {info.get('port')}",
                "status": "online",
                "miner_version": info.get("version", 0)
            }
            if hotkey in checking_allocated:
                entry.status = "offline"
                break

            allocation_list.append(entry)
        return allocation_list

    except Exception as e:
        return {"error": f"Error occurred while retrieving allocation details: {e}"}
    finally:
        cursor.close()
