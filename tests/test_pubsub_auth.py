"""
Unit tests for PubSub validator gateway authentication.

These tests verify that the validator gateway authentication flow works correctly
without requiring external dependencies.
"""

import pytest
import sys
import os
from unittest.mock import Mock, patch, MagicMock
from typing import Dict, Any

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from api.services.pubsub.auth import ValidatorGatewayAuth
from api.services.pubsub.client import PubSubClient
from api.services.pubsub.exceptions import AuthenticationError, ConfigurationError


class TestValidatorGatewayAuth:
    """Test cases for ValidatorGatewayAuth class."""

    def setup_method(self):
        """Set up test fixtures."""
        # Mock wallet
        self.mock_wallet = Mock()
        self.mock_wallet.hotkey.ss58_address = "5TestHotkeyAddress123"
        self.mock_wallet.hotkey.sign.return_value.hex.return_value = "mock_signature_hex"
        
        # Mock config
        self.mock_config = Mock()
        self.mock_config.netuid = 27
        self.mock_config.subtensor.network = "test"

    def test_get_network_config_test_network(self):
        """Test network config for test network."""
        auth = ValidatorGatewayAuth(self.mock_wallet, self.mock_config)
        config = auth._get_network_config()
        
        assert config["domain"] == "https://validator-token-gateway-auth-development-pufph5srwa-uc.a.run.app"
        assert config["project_id"] == "ni-sn27-frontend-dev"

    def test_get_network_config_finney_network(self):
        """Test network config for finney network."""
        self.mock_config.subtensor.network = "finney"
        auth = ValidatorGatewayAuth(self.mock_wallet, self.mock_config)
        config = auth._get_network_config()
        
        assert config["domain"] == "https://validator-token-gateway-auth-production-pufph5srwa-uc.a.run.app"
        assert config["project_id"] == "ni-sn27-frontend"

    @patch('api.services.pubsub.auth.requests.post')
    def test_authenticate_validator_gateway_success(self, mock_post):
        """Test successful validator gateway authentication."""
        # Mock successful response
        mock_response = Mock()
        mock_response.json.return_value = {"access_token": "mock_jwt_token"}
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response
        
        auth = ValidatorGatewayAuth(self.mock_wallet, self.mock_config)
        jwt_token = auth._authenticate_validator_gateway()
        
        assert jwt_token == "mock_jwt_token"
        
        # Verify the request was made correctly
        mock_post.assert_called_once()
        call_args = mock_post.call_args
        assert "/auth/token" in call_args[0][0]
        assert "Authorization" in call_args[1]["headers"]
        assert "Bittensor 5TestHotkeyAddress123:mock_signature_hex" == call_args[1]["headers"]["Authorization"]

    @patch('api.services.pubsub.auth.requests.post')
    def test_authenticate_validator_gateway_failure(self, mock_post):
        """Test failed validator gateway authentication."""
        # Mock failed response
        mock_post.side_effect = Exception("Network error")
        
        auth = ValidatorGatewayAuth(self.mock_wallet, self.mock_config)
        
        with pytest.raises(AuthenticationError) as exc_info:
            auth._authenticate_validator_gateway()
        
        assert "Failed to authenticate with validator-token-gateway" in str(exc_info.value)

    @patch('api.services.pubsub.auth.requests.post')
    def test_get_pubsub_token_success(self, mock_post):
        """Test successful pubsub token retrieval."""
        # Mock successful response
        mock_response = Mock()
        mock_response.json.return_value = {"access_token": "mock_pubsub_token"}
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response
        
        auth = ValidatorGatewayAuth(self.mock_wallet, self.mock_config)
        auth.jwt_token = "mock_jwt_token"
        
        pubsub_token = auth._get_pubsub_token()
        
        assert pubsub_token == "mock_pubsub_token"
        
        # Verify the request was made correctly
        mock_post.assert_called_once()
        call_args = mock_post.call_args
        assert "/auth/pubsub-token" in call_args[0][0]
        assert "Authorization" in call_args[1]["headers"]
        assert "Bearer mock_jwt_token" == call_args[1]["headers"]["Authorization"]

    def test_get_pubsub_token_no_jwt(self):
        """Test pubsub token retrieval without JWT token."""
        auth = ValidatorGatewayAuth(self.mock_wallet, self.mock_config)
        
        with pytest.raises(AuthenticationError) as exc_info:
            auth._get_pubsub_token()
        
        assert "Cannot get Pub/Sub token: No JWT token available" in str(exc_info.value)

    @patch('api.services.pubsub.auth.Credentials')
    @patch.object(ValidatorGatewayAuth, '_get_pubsub_token')
    @patch.object(ValidatorGatewayAuth, '_authenticate_validator_gateway')
    def test_get_credentials_success(self, mock_auth, mock_pubsub, mock_credentials_class):
        """Test successful credential creation."""
        # Mock the authentication flow
        mock_auth.return_value = "mock_jwt_token"
        mock_pubsub.return_value = "mock_pubsub_token"
        
        # Mock credentials creation
        mock_credentials = Mock()
        mock_credentials_class.return_value = mock_credentials
        
        auth = ValidatorGatewayAuth(self.mock_wallet, self.mock_config)
        credentials = auth.get_credentials()
        
        assert credentials == mock_credentials
        mock_auth.assert_called_once()
        mock_pubsub.assert_called_once()
        mock_credentials_class.assert_called_once_with(
            token="mock_pubsub_token",
            scopes=["https://www.googleapis.com/auth/pubsub"]
        )

    def test_get_project_id(self):
        """Test project ID retrieval."""
        auth = ValidatorGatewayAuth(self.mock_wallet, self.mock_config)
        project_id = auth.get_project_id()
        
        assert project_id == "ni-sn27-frontend-dev"


class TestPubSubClientValidatorGateway:
    """Test cases for PubSubClient with validator gateway authentication."""

    def setup_method(self):
        """Set up test fixtures."""
        # Mock wallet
        self.mock_wallet = Mock()
        self.mock_wallet.hotkey.ss58_address = "5TestHotkeyAddress123"
        
        # Mock config
        self.mock_config = Mock()
        self.mock_config.netuid = 27
        self.mock_config.subtensor.network = "test"

    def test_init_missing_wallet_config(self):
        """Test initialization with missing wallet or config."""
        with pytest.raises(ConfigurationError) as exc_info:
            PubSubClient(use_validator_gateway=True)
        
        assert "wallet and config are required for validator gateway authentication" in str(exc_info.value)

    def test_init_service_account_missing_project_id(self):
        """Test initialization with missing project ID for service account."""
        with pytest.raises(ConfigurationError) as exc_info:
            PubSubClient(use_validator_gateway=False)
        
        assert "project_id is required for service account authentication" in str(exc_info.value)

    @patch('api.services.pubsub.client.pubsub_v1.PublisherClient')
    @patch('api.services.pubsub.client.pubsub_v1.SubscriberClient')
    @patch('api.services.pubsub.client.ValidatorGatewayAuth')
    def test_init_validator_gateway_success(self, mock_auth_class, mock_subscriber, mock_publisher):
        """Test successful initialization with validator gateway."""
        # Mock auth
        mock_auth = Mock()
        mock_auth.get_project_id.return_value = "test-project"
        mock_auth.get_credentials.return_value = Mock()
        mock_auth_class.return_value = mock_auth

        # Mock clients
        mock_publisher_instance = Mock()
        mock_subscriber_instance = Mock()
        mock_publisher.return_value = mock_publisher_instance
        mock_subscriber.return_value = mock_subscriber_instance

        client = PubSubClient(
            wallet=self.mock_wallet,
            config=self.mock_config,
            use_validator_gateway=True
        )

        assert client.use_validator_gateway is True
        assert client.project_id == "test-project"
        assert client.auth == mock_auth
        mock_auth_class.assert_called_once_with(self.mock_wallet, self.mock_config)

    @patch('api.services.pubsub.client.service_account.Credentials')
    @patch('api.services.pubsub.client.pubsub_v1.PublisherClient')
    @patch('api.services.pubsub.client.pubsub_v1.SubscriberClient')
    def test_init_service_account_success(self, mock_subscriber, mock_publisher, mock_credentials):
        """Test successful initialization with service account."""
        # Mock credentials
        mock_creds = Mock()
        mock_credentials.from_service_account_file.return_value = mock_creds
        
        # Mock clients
        mock_publisher_instance = Mock()
        mock_subscriber_instance = Mock()
        mock_publisher.return_value = mock_publisher_instance
        mock_subscriber.return_value = mock_subscriber_instance
        
        client = PubSubClient(
            project_id="test-project",
            credentials_path="/path/to/creds.json",
            use_validator_gateway=False
        )
        
        assert client.use_validator_gateway is False
        assert client.project_id == "test-project"
        assert client.auth is None

    @patch('api.services.pubsub.client.pubsub_v1.PublisherClient')
    @patch('api.services.pubsub.client.pubsub_v1.SubscriberClient')
    @patch('api.services.pubsub.client.ValidatorGatewayAuth')
    def test_refresh_credentials_success(self, mock_auth_class, mock_subscriber, mock_publisher):
        """Test successful credential refresh."""
        # Mock auth
        mock_auth = Mock()
        mock_auth.get_project_id.return_value = "test-project"
        mock_auth.get_credentials.return_value = Mock()
        mock_auth.refresh_tokens.return_value = None
        mock_auth_class.return_value = mock_auth

        # Mock clients
        mock_publisher.return_value = Mock()
        mock_subscriber.return_value = Mock()

        client = PubSubClient(
            wallet=self.mock_wallet,
            config=self.mock_config,
            use_validator_gateway=True
        )

        result = client.refresh_credentials()

        assert result is True
        mock_auth.refresh_tokens.assert_called_once()

    def test_refresh_credentials_not_validator_gateway(self):
        """Test credential refresh when not using validator gateway."""
        client = PubSubClient(
            project_id="test-project",
            use_validator_gateway=False
        )

        result = client.refresh_credentials()

        assert result is False

    @patch('api.services.pubsub.client.pubsub_v1.PublisherClient')
    @patch('api.services.pubsub.client.pubsub_v1.SubscriberClient')
    @patch('api.services.pubsub.client.ValidatorGatewayAuth')
    def test_queue_functionality(self, mock_auth_class, mock_subscriber, mock_publisher):
        """Test async queue functionality for validator gateway."""
        # Mock auth
        mock_auth = Mock()
        mock_auth.get_project_id.return_value = "test-project"
        mock_auth.get_credentials.return_value = Mock()
        mock_auth_class.return_value = mock_auth

        # Mock clients
        mock_publisher.return_value = Mock()
        mock_subscriber.return_value = Mock()

        client = PubSubClient(
            wallet=self.mock_wallet,
            config=self.mock_config,
            use_validator_gateway=True
        )

        # Test queue initialization
        assert client.queues is not None
        assert len(client.queues) == 4
        assert "allocation-events" in client.queues
        assert "miner-events" in client.queues
        assert "system-events" in client.queues
        assert "validation-events" in client.queues

        # Test queue status
        queue_status = client.get_queue_status()
        assert isinstance(queue_status, dict)
        assert all(size == 0 for size in queue_status.values())  # All queues should be empty initially

    def test_service_account_no_queues(self):
        """Test that service account mode doesn't use queues."""
        client = PubSubClient(
            project_id="test-project",
            use_validator_gateway=False
        )

        # Test no queues for service account
        assert client.queues is None
        assert client._queue_workers == {}
        assert client._worker_shutdown is None

        # Test queue status returns empty dict
        queue_status = client.get_queue_status()
        assert queue_status == {}


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
