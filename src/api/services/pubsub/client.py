"""
PubSub client that uses GCP's built-in acknowledgment mechanism.

This approach relies on GCP Pub/Sub's native delivery guarantees:
- Messages are delivered until subscriber calls message.ack()
- No ack = automatic retry by GCP
- Ack = message successfully processed and removed from queue
"""

import json
import logging
import asyncio
from typing import Optional, Dict, Any, Callable, Union, List
from google.cloud import pubsub_v1
from google.oauth2 import service_account
from google.api_core import exceptions as gcp_exceptions
import time

from .message_types import (
    BasePubSubMessage,
    RegisterApiPubSubMessage,
    TOPICS,
)
from .exceptions import (
    PublishError,
    AuthenticationError,
    TopicNotFoundError,
    ConfigurationError,
)
from .auth import ValidatorGatewayAuth


class PubSubClient:
    """
    PubSub client that uses GCP's built-in acknowledgment system.

    This client publishes messages and optionally waits to see if they're
    being processed by monitoring subscription metrics.
    """

    def __init__(
        self,
        project_id: Optional[str] = None,
        credentials_path: Optional[str] = None,
        credentials_dict: Optional[Dict[str, Any]] = None,
        wallet=None,
        config=None,
        publish_timeout: float = 10.0,
        acknowledgment_timeout: float = 30.0,
        use_validator_gateway: bool = False,
    ):
        """
        Initialize the Pub/Sub client.

        Args:
            project_id: GCP project ID (optional if using validator gateway)
            credentials_path: Path to service account JSON file
            credentials_dict: Service account credentials as dict
            wallet: Bittensor wallet instance (required for validator gateway)
            config: Validator config (required for validator gateway)
            publish_timeout: Timeout for publish operations in seconds
            acknowledgment_timeout: Timeout for waiting for message acknowledgment in seconds
            use_validator_gateway: Whether to use validator gateway authentication
        """
        self.publish_timeout = publish_timeout
        self.acknowledgment_timeout = acknowledgment_timeout
        # Keep backward compatibility
        self.timeout = publish_timeout
        self.logger = logging.getLogger(__name__)
        self.use_validator_gateway = use_validator_gateway
        self.auth: Optional[ValidatorGatewayAuth] = None

        # Initialize authentication
        if use_validator_gateway:
            if not wallet or not config:
                raise ConfigurationError("wallet and config are required for validator gateway authentication")

            self.auth = ValidatorGatewayAuth(wallet, config)
            self.project_id = self.auth.get_project_id()

            # Initialize clients with validator gateway auth
            self._initialize_validator_gateway_clients()
        else:
            if not project_id:
                raise ConfigurationError("project_id is required for service account authentication")

            self.project_id = project_id

            # Initialize clients with service account credentials
            self._initialize_service_account_clients(credentials_path, credentials_dict)

        # Cache topic paths
        self._topic_paths = {
            TOPICS.ALLOCATION_EVENTS: f"projects/{self.project_id}/topics/{TOPICS.ALLOCATION_EVENTS}",
            TOPICS.MINER_EVENTS: f"projects/{self.project_id}/topics/{TOPICS.MINER_EVENTS}",
            TOPICS.SYSTEM_EVENTS: f"projects/{self.project_id}/topics/{TOPICS.SYSTEM_EVENTS}",
            TOPICS.VALIDATION_EVENTS: f"projects/{self.project_id}/topics/{TOPICS.VALIDATION_EVENTS}",
        }

        # Message queues for reliable publishing (only for validator gateway)
        if self.use_validator_gateway:
            self.queues = {
                TOPICS.ALLOCATION_EVENTS: asyncio.Queue(),
                TOPICS.MINER_EVENTS: asyncio.Queue(),
                TOPICS.SYSTEM_EVENTS: asyncio.Queue(),
                TOPICS.VALIDATION_EVENTS: asyncio.Queue(),
            }
            # Background workers for processing queues
            self._queue_workers: Dict[str, asyncio.Task] = {}
            self._worker_shutdown = asyncio.Event()

            # Start background queue workers
            self._start_queue_workers()
        else:
            self.queues = None
            self._queue_workers = {}
            self._worker_shutdown = None

    def _initialize_service_account_clients(
        self,
        credentials_path: Optional[str],
        credentials_dict: Optional[Dict[str, Any]]
    ):
        """Initialize clients using service account credentials."""
        try:
            # Initialize credentials
            if credentials_path:
                credentials = service_account.Credentials.from_service_account_file(
                    credentials_path
                )
            elif credentials_dict:
                credentials = service_account.Credentials.from_service_account_info(
                    credentials_dict
                )
            else:
                credentials = None

            # Initialize publisher and subscriber clients
            self.publisher = pubsub_v1.PublisherClient(credentials=credentials)
            self.subscriber = pubsub_v1.SubscriberClient(credentials=credentials)

        except Exception as e:
            raise AuthenticationError(f"Failed to initialize Pub/Sub client with service account: {e}")

    def _initialize_validator_gateway_clients(self, max_retries: int = 3):
        """Initialize clients using validator gateway authentication."""
        last_error = None
        for attempt in range(max_retries):
            try:
                credentials = self.auth.get_credentials()

                # Initialize clients
                self.publisher = pubsub_v1.PublisherClient(credentials=credentials)
                self.subscriber = pubsub_v1.SubscriberClient(credentials=credentials)

                self.logger.info("Successfully initialized Pub/Sub client with validator gateway auth")
                return

            except Exception as e:
                last_error = e
                self.logger.warning(f"Failed to initialize validator gateway clients (attempt {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    # Exponential backoff: 2, 4, 8 seconds
                    backoff_time = 2 ** attempt
                    self.logger.info(f"Retrying in {backoff_time} seconds...")
                    time.sleep(backoff_time)

        # All retries failed
        raise AuthenticationError(f"Failed to initialize validator gateway clients after {max_retries} attempts. Last error: {last_error}")

    def refresh_credentials(self, max_retries: int = 3) -> bool:
        """
        Refresh authentication tokens and reinitialize clients (validator gateway only).

        Returns:
            True if successful, False if all retries failed
        """
        if not self.use_validator_gateway:
            self.logger.warning("Cannot refresh credentials: not using validator gateway authentication")
            return False

        last_error = None
        for attempt in range(max_retries):
            try:
                self.auth.refresh_tokens()
                self._initialize_validator_gateway_clients(max_retries=1)  # Don't double-retry initialization
                self.logger.info("Successfully refreshed Pub/Sub credentials")
                return True
            except Exception as e:
                last_error = e
                self.logger.warning(f"Failed to refresh credentials (attempt {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    # Exponential backoff: 2, 4, 8 seconds
                    backoff_time = 2 ** attempt
                    self.logger.info(f"Retrying credential refresh in {backoff_time} seconds...")
                    time.sleep(backoff_time)

        # All retries failed
        self.logger.error(f"Failed to refresh credentials after {max_retries} attempts. Last error: {last_error}")
        return False

    def _start_queue_workers(self):
        """Start background workers to process message queues."""
        if not self.use_validator_gateway:
            return

        try:
            asyncio.get_running_loop()
            # We're in an async context, create tasks
            for topic_name in self.queues.keys():
                worker = asyncio.create_task(self._queue_worker(topic_name))
                self._queue_workers[topic_name] = worker
                self.logger.info(f"Started queue worker for {topic_name}")
        except RuntimeError:
            # No running loop, workers will start when first message is queued
            self.logger.debug("No running event loop, queue workers will start when needed")

    async def _ensure_workers_started(self):
        """Ensure queue workers are started."""
        if not self.use_validator_gateway or self._queue_workers:
            return

        for topic_name in self.queues.keys():
            worker = asyncio.create_task(self._queue_worker(topic_name))
            self._queue_workers[topic_name] = worker

    async def _queue_worker(self, topic_name: str):
        """Background worker to process messages from a queue."""
        if not self.use_validator_gateway:
            return

        queue = self.queues[topic_name]
        while not self._worker_shutdown.is_set():
            try:
                message_data = await asyncio.wait_for(queue.get(), timeout=1.0)

                # Try to publish with retry
                if await self._publish_with_retry_from_queue(topic_name, message_data):
                    queue.task_done()
                else:
                    # Put back and wait before retry
                    await queue.put(message_data)
                    queue.task_done()
                    await asyncio.sleep(5.0)

            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self.logger.error(f"Error in queue worker for {topic_name}: {e}")
                await asyncio.sleep(1.0)

    async def _publish_message_with_retry(
        self,
        topic_name: str,
        message: BasePubSubMessage,
    ) -> str:
        """
        Publish a message with retry and token refresh for validator gateway.

        Args:
            topic_name: Name of the topic to publish to
            message: Message to publish

        Returns:
            Message ID of the published message
        """
        for attempt in range(3):
            try:
                return await self._publish_message(topic_name, message)
            except gcp_exceptions.Unauthenticated:
                if self.use_validator_gateway:
                    self.logger.warning(f"Token expired for {topic_name}, refreshing credentials...")
                    if self.refresh_credentials():
                        # Credentials refreshed successfully, retry the publish
                        continue
                    else:
                        # Credential refresh failed after retries
                        raise AuthenticationError(f"Cannot publish to {topic_name}: credential refresh failed")
                else:
                    # Not using validator gateway, re-raise the error
                    raise
            except Exception as e:
                if attempt < 2:
                    self.logger.warning(f"Publish to {topic_name} failed (attempt {attempt + 1}): {e}")
                    await asyncio.sleep(2 ** attempt)
                else:
                    raise

        raise PublishError(f"Failed to publish to {topic_name} after 3 attempts")

    async def _publish_with_retry_from_queue(self, topic_name: str, message_data: bytes) -> bool:
        """Publish message from queue with retry and token refresh."""
        # Ensure clients are initialized
        if not self._ensure_clients_initialized():
            return False

        if topic_name not in self._topic_paths:
            self.logger.error(f"Topic {topic_name} not found in topic paths")
            return False

        for attempt in range(3):
            try:
                future = self.publisher.publish(self._topic_paths[topic_name], message_data)
                future.result(timeout=self.publish_timeout)
                return True
            except gcp_exceptions.Unauthenticated:
                if self.use_validator_gateway:
                    self.logger.warning(f"Token expired for {topic_name}, refreshing credentials...")
                    if self.refresh_credentials():
                        # Credentials refreshed successfully, retry the publish
                        continue
                    else:
                        # Credential refresh failed after retries
                        self.logger.error(f"Cannot publish to {topic_name}: credential refresh failed")
                        return False
                else:
                    # Not using validator gateway, re-raise the error
                    return False
            except Exception as e:
                self.logger.error(f"Publish to {topic_name} failed (attempt {attempt + 1}): {e}")
                if attempt < 2:
                    await asyncio.sleep(2 ** attempt)

        return False

    def _ensure_clients_initialized(self) -> bool:
        """Ensure clients are initialized, retry if needed."""
        # Check if we have both publisher and subscriber
        if self.publisher and self.subscriber:
            return True

        if not self.use_validator_gateway:
            # For service account, clients should already be initialized
            return self.publisher is not None

        self.logger.info("Pub/Sub clients not initialized, attempting to initialize...")
        try:
            self._initialize_validator_gateway_clients()
            return True
        except AuthenticationError as e:
            self.logger.error(f"Failed to initialize clients: {e}")
            return False

    async def _publish_message_to_queue(self, topic_name: str, message: BasePubSubMessage) -> str:
        """Queue a message for publishing (validator gateway only)."""
        if not self.use_validator_gateway:
            raise ConfigurationError("Queue-based publishing only available with validator gateway authentication")

        if topic_name not in self.queues:
            raise ConfigurationError(f"Unknown topic: {topic_name}")

        # Ensure workers are started
        await self._ensure_workers_started()

        # Convert message to JSON
        message_data = json.dumps(message.to_dict()).encode('utf-8')

        # Queue the message
        await self.queues[topic_name].put(message_data)

        return f"queued-{int(time.time() * 1000)}"

    async def _publish_message(
        self,
        topic_name: str,
        message: BasePubSubMessage,
    ) -> str:
        """
        Publish a message to the specified topic.

        Args:
            topic_name: Name of the topic to publish to
            message: Message to publish

        Returns:
            Message ID of the published message
        """
        try:
            topic_path = self._topic_paths.get(topic_name)
            if not topic_path:
                raise TopicNotFoundError(f"Topic {topic_name} not found")

            # Convert message to JSON
            message_data = json.dumps(message.to_dict()).encode('utf-8')

            # Prepare message attributes
            attributes = {
                'messageType': message.message_type,
                'timestamp': message.timestamp,
                'source': message.source,
            }
            if message.priority and message.priority != 'normal':
                attributes['priority'] = message.priority
            if message.correlation_id:
                attributes['correlation_id'] = message.correlation_id

            # Publish message
            future = self.publisher.publish(
                topic_path,
                data=message_data,
                **attributes
            )

            # Wait for publish to complete
            message_id = future.result(timeout=self.publish_timeout)

            self.logger.info(
                f"Published {message.message_type} message to {topic_name} with ID: {message_id}"
            )
            return message_id

        except Exception as e:
            error_msg = f"Failed to publish message to {topic_name}: {e}"
            self.logger.error(error_msg)
            raise PublishError(error_msg)

    async def publish_with_fallback(
        self,
        topic_name: str,
        message: BasePubSubMessage,
        fallback_callback: Optional[Callable] = None,
        monitor_acknowledgment: bool = True,
        acknowledgment_timeout: Optional[float] = None,
    ) -> Dict[str, Any]:
        """
        Publish a message with acknowledgment monitoring and webhook fallback.

        This method implements the primary pubsub approach with webhook fallback:
        1. Attempts to publish to PubSub
        2. If publish succeeds, monitors for acknowledgment within timeout
        3. If acknowledgment timeout occurs, triggers webhook fallback
        4. If publish fails, immediately triggers webhook fallback

        Args:
            topic_name: Name of the topic to publish to
            message: Message to publish
            fallback_callback: Function to call if PubSub fails or times out
            monitor_acknowledgment: Whether to monitor acknowledgment (default: True)
            acknowledgment_timeout: Override default acknowledgment timeout

        Returns:
            Dict with status and details
        """
        ack_timeout = acknowledgment_timeout or self.acknowledgment_timeout

        try:
            # Step 1: Attempt to publish to PubSub
            self.logger.info(f"Publishing message to {topic_name} (publish_timeout={self.publish_timeout}s)")

            if self.use_validator_gateway:
                message_id = await self._publish_message_to_queue(topic_name, message)
            else:
                message_id = await self._publish_message_with_retry(topic_name, message)

            self.logger.info(f"PubSub message published successfully: {message_id}")

            # Step 2: Monitor acknowledgment if enabled
            if monitor_acknowledgment:
                self.logger.info(f"Monitoring acknowledgment for message {message_id} (timeout={ack_timeout}s)")

                acknowledgment_received = await self._monitor_message_delivery(
                    topic_name, message_id, ack_timeout
                )

                if acknowledgment_received:
                    # Success: Message published and acknowledged
                    result = {
                        "status": "acknowledged",
                        "method": "pubsub",
                        "message_id": message_id,
                        "topic": topic_name,
                        "fallback_used": False,
                        "published_at": time.time(),
                        "acknowledged_at": time.time(),
                    }

                    if message.correlation_id:
                        result["correlation_id"] = message.correlation_id

                    self.logger.info(f"Message {message_id} acknowledged successfully")
                    return result
                else:
                    # Acknowledgment timeout: Trigger fallback
                    self.logger.warning(f"Message {message_id} not acknowledged within {ack_timeout}s, triggering fallback")

                    if fallback_callback:
                        try:
                            fallback_result = await fallback_callback()
                            return {
                                "status": "success",
                                "method": "fallback",
                                "fallback_result": fallback_result,
                                "fallback_used": True,
                                "fallback_reason": "acknowledgment_timeout",
                                "pubsub_message_id": message_id,
                                "acknowledgment_timeout": ack_timeout,
                                "published_at": time.time(),
                            }
                        except Exception as fallback_error:
                            return {
                                "status": "failed",
                                "method": "both_failed",
                                "fallback_reason": "acknowledgment_timeout",
                                "pubsub_message_id": message_id,
                                "fallback_error": str(fallback_error),
                                "fallback_used": True,
                                "published_at": time.time(),
                            }
                    else:
                        return {
                            "status": "timeout",
                            "method": "pubsub_timeout",
                            "message_id": message_id,
                            "acknowledgment_timeout": ack_timeout,
                            "fallback_used": False,
                            "published_at": time.time(),
                        }
            else:
                # No acknowledgment monitoring: Return success after publish
                result = {
                    "status": "published",
                    "method": "pubsub",
                    "message_id": message_id,
                    "topic": topic_name,
                    "fallback_used": False,
                    "published_at": time.time(),
                }

                if message.correlation_id:
                    result["correlation_id"] = message.correlation_id

                return result

        except Exception as e:
            # Publish failed: Trigger immediate fallback
            self.logger.warning(f"PubSub publish failed: {e}, triggering immediate fallback")

            if fallback_callback:
                try:
                    fallback_result = await fallback_callback()
                    return {
                        "status": "success",
                        "method": "fallback",
                        "fallback_result": fallback_result,
                        "fallback_used": True,
                        "fallback_reason": "publish_failed",
                        "pubsub_error": str(e),
                        "published_at": time.time(),
                    }
                except Exception as fallback_error:
                    return {
                        "status": "failed",
                        "method": "both_failed",
                        "fallback_reason": "publish_failed",
                        "pubsub_error": str(e),
                        "fallback_error": str(fallback_error),
                        "fallback_used": True,
                        "published_at": time.time(),
                    }
            else:
                return {
                    "status": "failed",
                    "method": "pubsub_only",
                    "error": str(e),
                    "fallback_used": False,
                    "published_at": time.time(),
                }

    async def _monitor_message_delivery(
        self,
        topic_name: str,
        message_id: str,
        timeout: float,
    ) -> bool:
        """
        Monitor if a message is being processed by checking subscription metrics.

        This method waits for the specified timeout to see if the message gets
        acknowledged by subscribers. If no acknowledgment is received within
        the timeout, it returns False to trigger fallback.

        Args:
            topic_name: Name of the topic the message was published to
            message_id: ID of the published message
            timeout: How long to wait for acknowledgment in seconds

        Returns:
            True if message was acknowledged, False if timeout occurred
        """
        try:
            # Get subscription names for this topic
            subscription_names = self._get_topic_subscriptions(topic_name)

            if not subscription_names:
                self.logger.warning(f"No subscriptions found for topic {topic_name}, cannot monitor delivery")
                # If no subscriptions, wait a short time and assume delivered
                await asyncio.sleep(min(timeout, 2.0))
                return True

            # Monitor each subscription for message acknowledgment
            start_time = time.time()
            check_interval = min(1.0, timeout / 10)  # Check every second or 1/10th of timeout

            while (time.time() - start_time) < timeout:
                all_acknowledged = True

                for subscription_name in subscription_names:
                    if not await self._check_message_acknowledged(subscription_name, message_id):
                        all_acknowledged = False
                        break

                if all_acknowledged:
                    self.logger.info(f"Message {message_id} acknowledged by all subscriptions")
                    return True

                # Wait before next check
                await asyncio.sleep(check_interval)

            # Timeout reached without acknowledgment
            self.logger.warning(f"Message {message_id} not acknowledged within {timeout}s timeout")
            return False

        except Exception as e:
            self.logger.error(f"Error monitoring message delivery for {message_id}: {e}")
            # On error, assume not delivered to trigger fallback
            return False

    def _get_topic_subscriptions(self, topic_name: str) -> List[str]:
        """
        Get list of subscription names for a given topic.

        Args:
            topic_name: Name of the topic

        Returns:
            List of subscription names
        """
        try:
            if not hasattr(self, 'subscriber') or not self.subscriber:
                self.logger.warning("No subscriber client available for monitoring")
                return []

            topic_path = self._topic_paths.get(topic_name)
            if not topic_path:
                return []

            # List subscriptions for the topic
            subscriptions = []
            try:
                for subscription in self.subscriber.list_subscriptions(
                    request={"project": f"projects/{self.project_id}"}
                ):
                    if subscription.topic == topic_path:
                        # Extract subscription name from full path
                        subscription_name = subscription.name.split('/')[-1]
                        subscriptions.append(subscription_name)
            except Exception as e:
                self.logger.warning(f"Could not list subscriptions for topic {topic_name}: {e}")

            return subscriptions

        except Exception as e:
            self.logger.error(f"Error getting subscriptions for topic {topic_name}: {e}")
            return []

    async def _check_message_acknowledged(self, subscription_name: str, message_id: str) -> bool:
        """
        Check if a specific message has been acknowledged by a subscription.

        This is a simplified implementation. In practice, you might need to use
        GCP monitoring APIs or other methods to track message acknowledgment.

        Args:
            subscription_name: Name of the subscription
            message_id: ID of the message to check

        Returns:
            True if message is acknowledged, False otherwise
        """
        try:
            # This is a simplified approach - in a real implementation you might:
            # 1. Use GCP monitoring APIs to check unacknowledged message counts
            # 2. Track message IDs in a database with acknowledgment status
            # 3. Use subscription metrics to determine if messages are being processed

            # For now, we'll use a heuristic: if the subscription exists and is active,
            # we assume messages are being processed. This could be enhanced with
            # actual monitoring APIs.

            subscription_path = f"projects/{self.project_id}/subscriptions/{subscription_name}"

            # Try to get subscription info to verify it exists and is active
            if hasattr(self, 'subscriber') and self.subscriber:
                try:
                    subscription_info = self.subscriber.get_subscription(
                        request={"subscription": subscription_path}
                    )
                    # If subscription exists and is not detached, assume message processing
                    return subscription_info.detached is False
                except Exception:
                    # If we can't get subscription info, assume not acknowledged
                    return False

            return False

        except Exception as e:
            self.logger.error(f"Error checking message acknowledgment for {message_id}: {e}")
            return False

    # Topic-specific publish methods with acknowledgment monitoring
    async def publish_to_allocation_events(
        self,
        message: RegisterApiPubSubMessage,
        fallback_callback: Optional[Callable] = None,
        monitor_acknowledgment: bool = True,
        acknowledgment_timeout: Optional[float] = None,
    ) -> Dict[str, Any]:
        """Publish to allocation-events topic with acknowledgment monitoring."""
        return await self.publish_with_fallback(
            TOPICS.ALLOCATION_EVENTS,
            message,
            fallback_callback,
            monitor_acknowledgment,
            acknowledgment_timeout
        )

    async def publish_to_miner_events(
        self,
        message: RegisterApiPubSubMessage,
        fallback_callback: Optional[Callable] = None,
        monitor_acknowledgment: bool = True,
        acknowledgment_timeout: Optional[float] = None,
    ) -> Dict[str, Any]:
        """Publish to miner-events topic with acknowledgment monitoring."""
        return await self.publish_with_fallback(
            TOPICS.MINER_EVENTS,
            message,
            fallback_callback,
            monitor_acknowledgment,
            acknowledgment_timeout
        )

    async def publish_to_system_events(
        self,
        message: RegisterApiPubSubMessage,
        fallback_callback: Optional[Callable] = None,
        monitor_acknowledgment: bool = True,
        acknowledgment_timeout: Optional[float] = None,
    ) -> Dict[str, Any]:
        """Publish to system-events topic with acknowledgment monitoring."""
        return await self.publish_with_fallback(
            TOPICS.SYSTEM_EVENTS,
            message,
            fallback_callback,
            monitor_acknowledgment,
            acknowledgment_timeout
        )

    async def publish_to_validation_events(
        self,
        message: RegisterApiPubSubMessage,
        fallback_callback: Optional[Callable] = None,
        monitor_acknowledgment: bool = True,
        acknowledgment_timeout: Optional[float] = None,
    ) -> Dict[str, Any]:
        """Publish to validation-events topic with acknowledgment monitoring."""
        return await self.publish_with_fallback(
            TOPICS.VALIDATION_EVENTS,
            message,
            fallback_callback,
            monitor_acknowledgment,
            acknowledgment_timeout
        )

    def get_queue_status(self) -> Dict[str, int]:
        """Get current queue sizes."""
        if not self.use_validator_gateway or not self.queues:
            return {}
        return {topic: queue.qsize() for topic, queue in self.queues.items()}

    async def stop_queue_workers(self):
        """Stop all background queue workers."""
        if not self.use_validator_gateway or not self._worker_shutdown:
            return

        self._worker_shutdown.set()
        if self._queue_workers:
            await asyncio.gather(*self._queue_workers.values(), return_exceptions=True)
            self._queue_workers.clear()

        # Log any remaining messages
        queue_status = self.get_queue_status()
        remaining = sum(queue_status.values())
        if remaining > 0:
            self.logger.warning(f"Stopping with {remaining} unpublished messages: {queue_status}")
        else:
            self.logger.info("All queue workers stopped, no pending messages")

    def close(self):
        """Close the publisher and subscriber clients."""
        # Stop queue workers if using validator gateway
        if self.use_validator_gateway and self._worker_shutdown:
            self._worker_shutdown.set()

            # Log any remaining messages
            queue_status = self.get_queue_status()
            remaining = sum(queue_status.values())
            if remaining > 0:
                self.logger.warning(f"Closing with {remaining} unpublished messages: {queue_status}")

        if hasattr(self, 'publisher') and self.publisher:
            self.publisher.close()
        if hasattr(self, 'subscriber') and self.subscriber:
            self.subscriber.close()

    def __enter__(self):
        """Context manager entry."""
        return self

    def __exit__(self, _exc_type, _exc_val, _exc_tb):
        """Context manager exit."""
        self.close()
