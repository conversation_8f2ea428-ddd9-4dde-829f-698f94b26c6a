"""
Register API Pub/Sub Library

PubSub client and message factory for GPU allocation events.
"""

from .client import PubSubClient
from .auth import Validator<PERSON>atewayAuth
from .message_factory import (
    RegisterApiPubSubMessageFactory,
    create_allocation_started_message,
    create_allocation_ended_message,
    create_miner_offline_message,
    create_miner_online_message,
)
from .message_types import (
    BasePubSubMessage,
    GpuStatusChangePubSubMessage,
    GpuDeallocationPubSubMessage,
    RegisterApiPubSubMessage,
    TOPICS,
    MESSAGE_TYPES,
)
from .exceptions import (
    PubSubError,
    MessageValidationError,
    PublishError,
    ConnectionError,
    AuthenticationError,
    TopicNotFoundError,
    ConfigurationError,
)

__all__ = [
    # Main Components
    "PubSubClient",
    "ValidatorGatewayAuth",

    # Message Factory
    "RegisterApiPubSubMessageFactory",
    "create_allocation_started_message",
    "create_allocation_ended_message",
    "create_miner_offline_message",
    "create_miner_online_message",

    # Message Types
    "BasePubSubMessage",
    "GpuStatusChangePubSubMessage",
    "GpuDeallocationPubSubMessage",
    "RegisterApiPubSubMessage",
    "TOPICS",
    "MESSAGE_TYPES",

    # Exceptions
    "PubSubError",
    "MessageValidationError",
    "PublishError",
    "ConnectionError",
    "AuthenticationError",
    "TopicNotFoundError",
    "ConfigurationError",
]
