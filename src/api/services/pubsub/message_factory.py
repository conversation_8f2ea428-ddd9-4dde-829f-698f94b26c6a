"""
Message factory for creating type-safe pub/sub messages.

This module provides a convenient factory class for creating properly formatted
messages for register-api pub/sub communication.
"""

from datetime import datetime, timezone
from typing import Optional

from .message_types import (
    GpuStatusChangePubSubMessage,
    GpuDeallocationPubSubMessage,
)


class RegisterApiPubSubMessageFactory:
    """Factory class for creating register-api pub/sub messages with proper formatting."""

    def __init__(self, validator_hotkey: str):
        """
        Initialize the message factory.

        Args:
            validator_hotkey: The hotkey of the validator creating messages
        """
        self.validator_hotkey = validator_hotkey

    def _get_timestamp(self) -> str:
        """Get current timestamp in ISO 8601 format with timezone."""
        return datetime.now(timezone.utc).isoformat()

    def create_gpu_status_change(
        self,
        miner_hotkey: str,
        previous_status: str,
        current_status: str,
        allocation_uuid: Optional[str] = None,
        reason: Optional[str] = None,
        priority: str = "normal",
        correlation_id: Optional[str] = None,
    ) -> GpuStatusChangePubSubMessage:
        """
        Create a GPU status change pub/sub message.

        Args:
            miner_hotkey: The hotkey of the miner
            previous_status: Previous status
            current_status: Current status
            allocation_uuid: Optional allocation UUID
            reason: Optional reason for status change
            priority: Message priority
            correlation_id: Optional correlation ID for tracking

        Returns:
            GpuStatusChangePubSubMessage instance
        """
        return GpuStatusChangePubSubMessage(
            message_type="",  # Will be set in __post_init__
            timestamp=self._get_timestamp(),
            source="register-api",
            priority=priority,
            correlation_id=correlation_id,
            validator_hotkey=self.validator_hotkey,
            miner_hotkey=miner_hotkey,
            previous_status=previous_status,
            current_status=current_status,
            allocation_uuid=allocation_uuid,
            reason=reason,
        )

    def create_gpu_deallocation(
        self,
        miner_hotkey: str,
        allocation_uuid: str,
        deallocation_reason: str,
        gpu_model: Optional[str] = None,
        allocation_duration_minutes: Optional[int] = None,
        user_id: Optional[str] = None,
        allocation_start_time: Optional[str] = None,
        priority: str = "high",
        correlation_id: Optional[str] = None,
    ) -> GpuDeallocationPubSubMessage:
        """
        Create a GPU deallocation pub/sub message.

        Args:
            miner_hotkey: The hotkey of the miner
            allocation_uuid: Allocation UUID
            deallocation_reason: Reason for deallocation
            gpu_model: Optional GPU model name
            allocation_duration_minutes: Optional duration of allocation
            user_id: Optional user ID who had the allocation
            allocation_start_time: Optional allocation start time
            priority: Message priority
            correlation_id: Optional correlation ID for tracking

        Returns:
            GpuDeallocationPubSubMessage instance
        """
        return GpuDeallocationPubSubMessage(
            message_type="",  # Will be set in __post_init__
            timestamp=self._get_timestamp(),
            source="register-api",
            priority=priority,
            correlation_id=correlation_id,
            validator_hotkey=self.validator_hotkey,
            miner_hotkey=miner_hotkey,
            allocation_uuid=allocation_uuid,
            deallocation_reason=deallocation_reason,
            gpu_model=gpu_model,
            allocation_duration_minutes=allocation_duration_minutes,
            user_id=user_id,
            allocation_start_time=allocation_start_time,
        )


# Convenience functions for common status changes
def create_allocation_started_message(
    factory: RegisterApiPubSubMessageFactory,
    miner_hotkey: str,
    allocation_uuid: str,
    user_id: Optional[str] = None,
    correlation_id: Optional[str] = None,
) -> GpuStatusChangePubSubMessage:
    """Create a pub/sub message for when GPU allocation starts."""
    return factory.create_gpu_status_change(
        miner_hotkey=miner_hotkey,
        previous_status="online",
        current_status="allocated",
        allocation_uuid=allocation_uuid,
        reason=f"allocation_started_for_user_{user_id}" if user_id else "allocation_started",
        priority="high",
        correlation_id=correlation_id,
    )


def create_allocation_ended_message(
    factory: RegisterApiPubSubMessageFactory,
    miner_hotkey: str,
    allocation_uuid: str,
    reason: str = "allocation_completed",
    correlation_id: Optional[str] = None,
) -> GpuStatusChangePubSubMessage:
    """Create a pub/sub message for when GPU allocation ends."""
    return factory.create_gpu_status_change(
        miner_hotkey=miner_hotkey,
        previous_status="allocated",
        current_status="online",
        allocation_uuid=allocation_uuid,
        reason=reason,
        priority="normal",
        correlation_id=correlation_id,
    )


def create_miner_offline_message(
    factory: RegisterApiPubSubMessageFactory,
    miner_hotkey: str,
    reason: str = "miner_disconnected",
    correlation_id: Optional[str] = None,
) -> GpuStatusChangePubSubMessage:
    """Create a pub/sub message for when miner goes offline."""
    return factory.create_gpu_status_change(
        miner_hotkey=miner_hotkey,
        previous_status="online",
        current_status="offline",
        reason=reason,
        priority="high",
        correlation_id=correlation_id,
    )


def create_miner_online_message(
    factory: RegisterApiPubSubMessageFactory,
    miner_hotkey: str,
    reason: str = "miner_reconnected",
    correlation_id: Optional[str] = None,
) -> GpuStatusChangePubSubMessage:
    """Create a pub/sub message for when miner comes online."""
    return factory.create_gpu_status_change(
        miner_hotkey=miner_hotkey,
        previous_status="offline",
        current_status="online",
        reason=reason,
        priority="normal",
        correlation_id=correlation_id,
    )
