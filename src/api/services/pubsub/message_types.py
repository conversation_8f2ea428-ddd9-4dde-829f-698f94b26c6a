"""
Message type definitions for Register API Pub/Sub communication.

This module contains all the message schemas and constants used for communication
between the register-api service and external systems via GCP Pub/Sub.

Adapted from the existing webhook message types but optimized for pub/sub usage.
"""

from typing import Dict, Any, Optional, Literal, Union
from dataclasses import dataclass, field
from datetime import datetime, timezone


# Topic constants matching SN27 topics
class TOPICS:
    ALLOCATION_EVENTS = "allocation-events"
    MINER_EVENTS = "miner-events"
    SYSTEM_EVENTS = "system-events"
    VALIDATION_EVENTS = "validation-events"


# Message type constants
class MESSAGE_TYPES:
    GPU_STATUS_CHANGE = "gpu_status_change"
    GPU_DEALLOCATION = "gpu_deallocation"


# Base message structure for pub/sub
@dataclass
class BasePubSubMessage:
    """Base class for all register-api pub/sub messages."""
    message_type: str = field()
    timestamp: str = field()  # ISO 8601 format
    source: Literal["register-api"] = field(default="register-api")
    priority: Optional[Literal["low", "normal", "high", "urgent"]] = "normal"
    correlation_id: Optional[str] = None
    data: Optional[Dict[str, Any]] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert message to dictionary for JSON serialization."""
        result = {
            "messageType": self.message_type,
            "timestamp": self.timestamp,
            "source": self.source,
            "data": self.data or {}
        }
        if self.priority != "normal":
            result["priority"] = self.priority
        if self.correlation_id:
            result["correlation_id"] = self.correlation_id
        return result


@dataclass
class GpuStatusChangePubSubMessage(BasePubSubMessage):
    """
    GPU status change message for pub/sub notifications.

    Used when miners change status (online/offline/testing/allocated) to notify
    external services via pub/sub topics.
    """
    # Required fields first
    validator_hotkey: str = field(default="")
    miner_hotkey: str = field(default="")
    previous_status: str = field(default="")
    current_status: str = field(default="")

    # Optional fields last
    allocation_uuid: Optional[str] = None
    reason: Optional[str] = None
    status_change_at: Optional[str] = None

    def __post_init__(self):
        self.message_type = MESSAGE_TYPES.GPU_STATUS_CHANGE
        self.source = "register-api"
        self.data = {
            "validator_hotkey": self.validator_hotkey,
            "miner_hotkey": self.miner_hotkey,
            "previous_status": self.previous_status,
            "current_status": self.current_status,
            "status_change_at": self.status_change_at or datetime.now(timezone.utc).isoformat(),
        }
        if self.allocation_uuid:
            self.data["allocation_uuid"] = self.allocation_uuid
        if self.reason:
            self.data["reason"] = self.reason


@dataclass
class GpuDeallocationPubSubMessage(BasePubSubMessage):
    """
    GPU deallocation message for pub/sub notifications.
    
    Used when GPU allocations are terminated to notify external services
    via pub/sub topics about the deallocation event.
    """
    # Required fields first
    validator_hotkey: str = field(default="")
    miner_hotkey: str = field(default="")
    allocation_uuid: str = field(default="")
    deallocation_reason: str = field(default="")
    
    # Optional fields last
    gpu_model: Optional[str] = None
    allocation_duration_minutes: Optional[int] = None
    deallocated_at: Optional[str] = None
    user_id: Optional[str] = None
    allocation_start_time: Optional[str] = None

    def __post_init__(self):
        self.message_type = MESSAGE_TYPES.GPU_DEALLOCATION
        self.source = "register-api"
        self.data = {
            "validator_hotkey": self.validator_hotkey,
            "miner_hotkey": self.miner_hotkey,
            "allocation_uuid": self.allocation_uuid,
            "deallocation_reason": self.deallocation_reason,
            "deallocated_at": self.deallocated_at or datetime.now(timezone.utc).isoformat(),
        }
        if self.gpu_model:
            self.data["gpu_model"] = self.gpu_model
        if self.allocation_duration_minutes:
            self.data["allocation_duration_minutes"] = self.allocation_duration_minutes
        if self.user_id:
            self.data["user_id"] = self.user_id
        if self.allocation_start_time:
            self.data["allocation_start_time"] = self.allocation_start_time


# Union type for all register-api pub/sub messages
RegisterApiPubSubMessage = Union[
    GpuStatusChangePubSubMessage,
    GpuDeallocationPubSubMessage,
]
