# Standard Library Imports
from collections import defaultdict
import math
from datetime import datetime, timezone

# Third-Party Imports
import bittensor as bt
from bittensor import StakeInfo
import urllib3
from urllib3.exceptions import InsecureRequestWarning
from fastapi import status
from fastapi.encoders import jsonable_encoder
from fastapi.responses import JSONResponse
from fastapi.concurrency import run_in_threadpool
from typing import Optional, List, Dict, Any

# Compute Subnet Libraries
from compute.axon import ComputeSubnetSubtensor
from compute.utils.db import ComputeDb
from compute.wandb.wandb import ComputeWandb
from compute import TRUSTED_VALIDATORS_HOTKEYS

# API Services and Models
from api.models.register import Resource, ResourceQuery
from api.services.sql_service import map_axon_ip_to_resources
from api.utils import _paginate_list
from api.constants import MINER_BLACKLIST

# Constants
from api.constants import PUBLIC_WANDB_ENTITY, PUBLIC_WANDB_NAME

# Custom Imports
from neurons.Validator.database.pog import get_pog_specs
from bittensor import Metagraph

# Disable SSL Warnings
urllib3.disable_warnings(InsecureRequestWarning)


async def count_all_gpus(
    config: Dict[str, Any], wandb: ComputeWandb, metagraph: Metagraph
) -> JSONResponse:
    """
    Count all GPUs on the compute subnet
    """
    bt.logging.info("API: Count Gpus(wandb) on compute subnet")
    GPU_COUNTS = 0
    # specs_details , running_hotkey = await get_wandb_running_miners(config, wandb, metagraph)
    specs_details, _ = await get_wandb_running_miners(config, wandb, metagraph)
    try:
        if specs_details:
            # Iterate through the miner specs details and print the table
            # for hotkey, details in specs_details.items():
            for _, details in specs_details.items():
                if details :
                    gpu_miner = details.get("gpu","")
                    # gpu_capacity = "{:.2f}".format(
                    #     (gpu_miner["capacity"] / 1024)
                    # )
                    # gpu_name = str(gpu_miner["details"][0]["name"]).lower()
                    gpu_count = gpu_miner.get("count",0)
                    GPU_COUNTS += gpu_count
            bt.logging.info("API: List resources successfully")
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "count": GPU_COUNTS,
            },
        )
    except Exception as e:
        bt.logging.error(f"API: An error occurred while counting GPUs: {e}")
        return JSONResponse(
            status_code=status.HTTP_403_FORBIDDEN,
            content={
                "success": False,
                "message": "An error occurred while counting GPUs.",
                "err_detail": e.__repr__(),
            },
        )


async def count_all_model(
    config: Dict[str, Any],
    wandb: ComputeWandb,
    metagraph: Metagraph,
    model: str,
    cpu_count: Optional[int] = None,
    ram_size: Optional[float] = None,
) -> JSONResponse:
    """
    Count all GPUs on the compute subnet
    """
    bt.logging.info("API: Count Gpus by model(wandb) on compute subnet")
    counter = 0
    # specs_details , running_hotkey = await get_wandb_running_miners(config, wandb, metagraph)
    specs_details, _ = await get_wandb_running_miners(config, wandb, metagraph)
    try:
        if specs_details:
            # Iterate through the miner specs details and print the table
            for hotkey, details in specs_details.items():
                flag = 0
                if details:
                    gpu_miner = details["gpu"]
                    gpu_details = gpu_miner.get("details", [])
                    # Check if details exist and is non-empty
                    if (
                        gpu_details
                        and isinstance(gpu_details, list)
                        and len(gpu_details) > 0
                    ):
                        gpu_name = str(gpu_details[0].get("name", "")).lower()
                    if model.lower() == gpu_name:
                        if cpu_count is not None:
                            cpu_miner = details["cpu"]
                            if cpu_miner.get("count") == cpu_count:
                                flag += 1
                        elif ram_size is not None:
                            ram_miner = details.get("ram", {})
                            ram = ram_miner.get("total", 0) / 1024.0**3
                            if int(math.ceil(ram)) == int(ram_size):
                                flag += 1
                        else:
                            flag += 1
                    if flag:
                        counter += 1
            bt.logging.info("API: List resources successfully")
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "count": counter,
            },
        )
    except Exception as e:
        bt.logging.error(f"API: An error occurred while counting GPUs: {e}")
        return JSONResponse(
            status_code=status.HTTP_403_FORBIDDEN,
            content={
                "success": False,
                "message": "An error occurred while counting GPUs.",
                "err_detail": e.__repr__(),
            },
        )


async def list_resources_wandb(
    db: ComputeDb,
    config: Dict[str, Any],
    wandb: ComputeWandb,
    metagraph: Metagraph,
    query: ResourceQuery = None,
    stats: bool = False,
    page_size: Optional[int] = None,
    page_number: Optional[int] = None,
) -> JSONResponse:
    """
    The list resources API endpoint.
    The API will return the current miner resource and their detail specs on the validator.
    query: The query parameter to filter the resources.
    """
    bt.logging.info("API: List resources(wandb) on compute subnet")
    wandb.api.flush()
    specs_details, running_hotkey = await get_wandb_running_miners(
        config, wandb, metagraph
    )
    bt.logging.info(f"API: Number of running miners: {len(running_hotkey)}")

    # Initialize a dictionary to keep track of GPU instances
    resource_list = []
    gpu_instances = {}
    total_gpu_counts = {}

    # Get the allocated hotkeys from wandb
    allocated_hotkeys = await run_in_threadpool(
        wandb.get_allocated_hotkeys, TRUSTED_VALIDATORS_HOTKEYS, True
    )
    bt.logging.info(f"API: Allocated hotkeys: {allocated_hotkeys}")
    bt.logging.info(f"API: Number of allocated hotkeys: {len(allocated_hotkeys)}")

    # get get_penalized_hotkeys_checklist
    penalized_hotkeys = wandb.get_penalized_hotkeys_checklist_bak([], True)  # get_penalized_hotkeys_checklist_bak will have NI validator hotkey hardcoded
    # penalized_hotkeys = await run_in_threadpool(self.get_penalized_hotkeys_checklist, valid_validator_hotkeys=[], flag=False)

    if specs_details:
        # Iterate through the miner specs details and print the table
        for hotkey, details in specs_details.items():

            # miner_older_than = _miner_is_older_than(db, 48, hotkey)
            miner_pog_ok = _miner_pog_ok(db=db, hours=2.5, ss58_address=hotkey)

            if hotkey in running_hotkey and miner_pog_ok and hotkey not in penalized_hotkeys:
                if details:  # Check if details are not empty
                    resource = Resource()
                    try:
                        # Extract GPU details
                        gpu_miner = details["gpu"]
                        gpu_capacity = "{:.2f}".format((gpu_miner["capacity"] / 1024))
                        gpu_name = str(gpu_miner["details"][0]["name"]).lower()
                        gpu_count = gpu_miner["count"]
                        # Extract CPU details
                        cpu_miner = details["cpu"]
                        cpu_count = cpu_miner["count"]
                        # Extract RAM details
                        ram_miner = details["ram"]
                        ram = "{:.2f}".format(ram_miner["total"] / 1024.0**3)
                        # Extract Hard Disk details
                        hard_disk_miner = details["hard_disk"]
                        hard_disk = "{:.2f}".format(
                            hard_disk_miner["free"] / 1024.0**3
                        )
                        # Update the GPU instances count
                        gpu_key = (gpu_name, gpu_count)
                        gpu_instances[gpu_key] = gpu_instances.get(gpu_key, 0) + 1
                        total_gpu_counts[gpu_name] = (
                            total_gpu_counts.get(gpu_name, 0) + gpu_count
                        )
                        version = details.get("version", 0)
                    except (KeyError, IndexError, TypeError):
                        gpu_name = "Invalid details"
                        gpu_capacity = "N/A"
                        gpu_count = "N/A"
                        cpu_count = "N/A"
                        ram = "N/A"
                        hard_disk = "N/A"
                else:
                    gpu_name = "No details available"
                    gpu_capacity = "N/A"
                    gpu_count = "N/A"
                    cpu_count = "N/A"
                    ram = "N/A"
                    hard_disk = "N/A"
                    version = "N/A"
                # Allocation status
                # allocate_status = "N/A"
                if hotkey in allocated_hotkeys:
                    allocate_status = "reserved"
                    if not stats:
                        continue
                else:
                    allocate_status = "available"
                add_resource = False
                # Print the row with column separators
                resource.hotkey = hotkey
                try:
                    if (
                        gpu_name != "Invalid details"
                        and gpu_name != "No details available"
                    ):
                        if query is None or query == {}:
                            add_resource = True
                        else:
                            if (
                                query.gpu_name is not None
                                and query.gpu_name.lower() not in gpu_name
                            ):
                                continue
                            if (
                                query.gpu_capacity_max is not None
                                and float(gpu_capacity) > query.gpu_capacity_max
                            ):
                                continue
                            if (
                                query.gpu_capacity_min is not None
                                and float(gpu_capacity) < query.gpu_capacity_min
                            ):
                                continue
                            if (
                                query.cpu_count_max is not None
                                and int(cpu_count) > query.cpu_count_max
                            ):
                                continue
                            if (
                                query.cpu_count_min is not None
                                and int(cpu_count) < query.cpu_count_min
                            ):
                                continue
                            if (
                                query.ram_total_max is not None
                                and float(ram) > query.ram_total_max
                            ):
                                continue
                            if (
                                query.ram_total_min is not None
                                and float(ram) < query.ram_total_min
                            ):
                                continue
                            if (
                                query.hard_disk_total_max is not None
                                and float(hard_disk) > query.hard_disk_total_max
                            ):
                                continue
                            if (
                                query.hard_disk_total_min is not None
                                and float(hard_disk) < query.hard_disk_total_min
                            ):
                                continue
                            add_resource = True
                        if add_resource:
                            resource.cpu_count = int(cpu_count)
                            resource.gpu_name = gpu_name
                            resource.gpu_capacity = float(gpu_capacity)
                            resource.gpu_count = int(gpu_count)
                            resource.ram = float(ram)
                            resource.hard_disk = float(hard_disk)
                            resource.allocate_status = allocate_status
                            resource.version = version
                            resource_list.append(resource)
                except (KeyError, IndexError, TypeError, ValueError) as e:
                    bt.logging.error(
                        f"API: Error occurred while filtering resources: {e}"
                    )
                    continue
        if stats:
            status_counts = {"available": 0, "reserved": 0, "total": 0}
            try:
                for item in resource_list:
                    status_code = item.dict()["allocate_status"]
                    if status_code in status_counts:
                        status_counts[status_code] += 1
                        status_counts["total"] += 1
            except Exception as e:
                bt.logging.error(f"API: Error occurred while counting status: {e}")
                status_counts = {"available": 0, "reserved": 0, "total": 0}
            bt.logging.info("API: List resources successfully")
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content={
                    "success": True,
                    "message": "List resources successfully",
                    "data": jsonable_encoder({"stats": status_counts}),
                },
            )
        else:
            bt.logging.info(f"API: Number of resources returned: {len(resource_list)}")
            bt.logging.trace("API: Resource List Contents:")
            for resource in resource_list:
                bt.logging.trace(vars(resource))
            if page_number:
                page_size = page_size if page_size else 50
                result = _paginate_list(resource_list, page_number, page_size)
            else:
                result = {
                    "page_items": resource_list,
                    "page_number": 1,
                    "page_size": len(resource_list),
                    "next_page_number": None,
                }
            bt.logging.info("API: List resources successfully")
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content={
                    "success": True,
                    "message": "List resources successfully",
                    "data": jsonable_encoder(result),
                },
            )
    else:
        bt.logging.info("API: There is no resource available")
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content={
                "success": False,
                "message": "There is no resource available",
                "err_detail": "No resources found.",
            },
        )


async def list_all_runs(
    config: Dict[str, Any],
    wandb: ComputeWandb,
    hotkey: Optional[str] = None,
    page_size: Optional[int] = None,
    page_number: Optional[int] = None,
) -> JSONResponse:
    """
    This function gets all run resources.
    """
    db_list = []
    try:
        # self.wandb.api.flush()
        if hotkey:
            filter_rule = {
                "$and": [
                    {"config.config.netuid": config.netuid},
                    {"config.hotkey": hotkey},
                    {"state": "running"},
                ]
            }
        else:
            filter_rule = {
                "$and": [
                    {"config.config.netuid": config.netuid},
                    {"state": "running"},
                ]
            }
        runs = await run_in_threadpool(
            wandb.api.runs, f"{PUBLIC_WANDB_ENTITY}/{PUBLIC_WANDB_NAME}", filter_rule
        )
        if runs:
            # Iterate over all runs in the opencompute project
            for index, run in enumerate(runs, start=1):
                # Access the run's configuration
                run_id = run.id
                run_name = run.name
                run_description = run.description
                run_config = run.config
                run_state = run.state
                # run_start_at = datetime.strptime(run.created_at, '%Y-%m-%dT%H:%M:%S')
                configs = run_config.get("config")
                append_entry = True
                # append the data to the db_list
                if configs and append_entry:
                    db_specs_dict = {
                        index: {
                            "id": run_id,
                            "name": run_name,
                            "description": run_description,
                            "configs": configs,
                            "state": run_state,
                            "start_at": run.created_at,
                        }
                    }
                    db_list.append(db_specs_dict)
            if page_number:
                page_size = page_size if page_size else 50
                result = _paginate_list(db_list, page_number, page_size)
            else:
                result = {
                    "page_items": db_list,
                    "page_number": 1,
                    "page_size": len(db_list),
                    "next_page_number": None,
                }
            bt.logging.info("API: List run resources successfully")
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content={
                    "success": True,
                    "message": "List run resources successfully.",
                    "data": jsonable_encoder(result),
                },
            )
        else:
            bt.logging.info("API: no runs available")
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content={
                    "success": True,
                    "message": "No runs available",
                    "data": {},
                },
            )
    except Exception as e:
        # Handle the exception by logging an error message
        bt.logging.error(f"API: An error occurred while getting specs from wandb: {e}")
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content={
                "success": False,
                "message": "Error occurred while getting runs from wandb",
                "err_detail": e.__repr__(),
            },
        )


async def list_specs(
    config: Dict[str, Any],
    wandb: ComputeWandb,
    hotkey: Optional[str] = None,
    page_size: Optional[int] = None,
    page_number: Optional[int] = None,
) -> JSONResponse:
    """
    The list specs API endpoint.
    """
    db_list = []
    try:
        # self.wandb.api.flush()
        if hotkey:
            filter_rule = {
                "$and": [
                    {"config.role": "miner"},
                    {"config.config.netuid": config.netuid},
                    {"state": "running"},
                    {"config.hotkey": hotkey},
                    {"config.specs": {"$exists": True}},
                ]
            }
        else:
            filter_rule = {
                "$and": [
                    {"config.role": "miner"},
                    {"config.config.netuid": config.netuid},
                    {"config.specs": {"$exists": True}},
                    {"state": "running"},
                ]
            }
        runs = await run_in_threadpool(
            wandb.api.runs, f"{PUBLIC_WANDB_ENTITY}/{PUBLIC_WANDB_NAME}", filter_rule
        )
        if runs:
            # Iterate over all runs in the opencompute project
            for index, run in enumerate(runs, start=1):
                # Access the run's configuration
                run_config = run.config
                run_state = run.state
                hotkey = run_config.get("hotkey")
                specs = run_config.get("specs")
                configs = run_config.get("config")
                # check the signature
                if hotkey and specs:
                    db_specs_dict = {
                        index: {
                            "hotkey": hotkey,
                            "configs": configs,
                            "specs": specs,
                            "state": run_state,
                        }
                    }
                    db_list.append(db_specs_dict)
            if page_number:
                page_size = page_size if page_size else 50
                result = _paginate_list(db_list, page_number, page_size)
            else:
                result = {
                    "page_items": db_list,
                    "page_number": 1,
                    "page_size": len(db_list),
                    "next_page_number": None,
                }
            # Return the db_specs_dict for further use or inspection
            bt.logging.info("API: List specs successfully")
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content={
                    "success": True,
                    "message": "List specs successfully",
                    "data": jsonable_encoder(result),
                },
            )
        else:
            bt.logging.info("API: no specs available")
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content={
                    "success": True,
                    "message": "No specs available",
                    "data": {},
                },
            )
    except Exception as e:
        # Handle the exception by logging an error message
        bt.logging.error(f"API: An error occurred while getting specs from wandb: {e}")
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content={
                "success": False,
                "message": "Error occurred while getting specs from wandb",
                "err_detail": e.__repr__(),
            },
        )


async def list_run_name(
    config: Dict[str, Any], wandb: ComputeWandb, run_name: str
) -> JSONResponse:
    """
    This function gets runs by name.
    """
    db_specs_dict = {}
    try:
        # self.wandb.api.flush()
        filter_rule = {
            "$and": [
                {"config.config.netuid": config.netuid},
                {"display_name": run_name},
                {"state": "running"},
            ]
        }
        runs = await run_in_threadpool(
            wandb.api.runs, f"{PUBLIC_WANDB_ENTITY}/{PUBLIC_WANDB_NAME}", filter_rule
        )
        if runs:
            # Iterate over all runs in the opencompute project
            for index, run in enumerate(runs, start=1):
                # Access the run's configuration
                run_id = run.id
                run_name = run.name
                run_description = run.description
                run_config = run.config
                hotkey = run_config.get("hotkey")
                configs = run_config.get("config")
                # check the signature
                if hotkey and configs:
                    db_specs_dict[index] = {
                        "id": run_id,
                        "name": run_name,
                        "description": run_description,
                        "config": configs,
                    }
            bt.logging.info("API: list run by name is success")
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content={
                    "success": True,
                    "message": "List run by name",
                    "data": jsonable_encoder(db_specs_dict),
                },
            )
        else:
            bt.logging.info("API: no run available")
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content={
                    "success": True,
                    "message": "No run available",
                    "data": {},
                },
            )
    except Exception as e:
        # Handle the exception by logging an error message
        bt.logging.error(f"API: An error occurred while getting specs from wandb: {e}")
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content={
                "success": False,
                "message": "Error occurred while run from wandb",
                "err_detail": e.__repr__(),
            },
        )


async def list_available_miner(
    wandb: ComputeWandb,
    config: Dict[str, Any],
    rent_status: bool = False,
    page_size: Optional[int] = None,
    page_number: Optional[int] = None,
) -> JSONResponse:
    """
    This function gets all available miners.
    """
    db_list = []
    try:
        wandb.api.flush()
        if rent_status:
            filter_rule = {
                "config.allocated": {"$regex": "\\d.*"},
                "config.config.netuid": config.netuid,
                "config.role": "miner",
                "state": "running",
            }
        else:
            filter_rule = {
                "$or": [
                    {"config.allocated": {"$regex": "null"}},
                    {"config.allocated": {"$exists": False}},
                ],
                "config.config.netuid": config.netuid,
                "config.role": "miner",
                "state": "running",
            }
        runs = await run_in_threadpool(
            wandb.api.runs, f"{PUBLIC_WANDB_ENTITY}/{PUBLIC_WANDB_NAME}", filter_rule
        )
        if runs:
            # Iterate over all runs in the opencompute project
            for index, run in enumerate(runs, start=1):
                # Access the run's configuration
                run_config = run.config
                hotkey = run_config.get("hotkey")
                specs = run.config.get("specs")
                configs = run_config.get("config")
                # check the signature
                if hotkey and configs:
                    db_specs_dict = {index: {"hotkey": hotkey, "details": specs}}
                    db_list.append(db_specs_dict)
            if page_number:
                page_size = page_size if page_size else 50
                result = _paginate_list(db_list, page_number, page_size)
            else:
                result = {
                    "page_items": db_list,
                    "page_number": 1,
                    "page_size": len(db_list),
                    "next_page_number": None,
                }
        else:
            bt.logging.info("API: No available miners")
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content={
                    "success": True,
                    "message": "No available miner",
                    "data": {},
                },
            )
        if rent_status:
            bt.logging.info("API: List rented miners is success")
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content={
                    "success": True,
                    "message": "List rented miners",
                    "data": jsonable_encoder(result),
                },
            )
        else:
            bt.logging.info("API: List available miners is success")
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content={
                    "success": True,
                    "message": "List available miners",
                    "data": jsonable_encoder(result),
                },
            )
    except Exception as e:
        # Handle the exception by logging an error message
        bt.logging.error(
            f"API: An error occurred while fetching available miner from wandb: {e}"
        )
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content={
                "success": False,
                "message": "Error occurred while fetching available miner from wandb",
                "err_detail": e.__repr__(),
            },
        )


async def list_allocated_hotkeys(
    wandb: ComputeWandb, config: Dict[str, Any]
) -> JSONResponse:
    """
    This function gets all allocated hotkeys from all validators.
    Only relevant for validators.
    """
    try:
        wandb.api.flush()
        filter_rule = {
            "$and": [
                {"config.role": "validator"},
                {"config.config.netuid": config.netuid},
                {"config.allocated_hotkeys": {"$regex": "\\d.*"}},
                {"state": "running"},
            ]
        }
        # Query all runs in the project and Filter runs where the role is 'validator'
        validator_runs = await run_in_threadpool(
            wandb.api.runs, f"{PUBLIC_WANDB_ENTITY}/{PUBLIC_WANDB_NAME}", filter_rule
        )
        # Check if the runs list is empty
        if not validator_runs:
            bt.logging.info(
                "API: No validator with allocated info in the project opencompute."
            )
            return JSONResponse(
                status_code=status.HTTP_404_NOT_FOUND,
                content={
                    "success": False,
                    "message": "No validator with allocated info in the project opencompute.",
                    "data": {},
                },
            )
    except Exception as e:
        bt.logging.error(f"API: list_allocated_hotkeys error with {e.__repr__()}")
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content={
                "success": False,
                "message": "Error occurred while fetching allocated hotkey from wandb",
                "err_detail": e.__repr__(),
            },
        )
    # Initialize an empty list to store allocated keys from runs with a valid signature
    allocated_keys_list = []
    # Verify the signature for each validator run
    for run in validator_runs:
        try:
            # Access the run's configuration
            run_config = run.config
            # hotkey = run_config.get("hotkey")
            allocated_keys = run_config.get("allocated_hotkeys")
            # id = run_config.get("id")
            # name = run_config.get("name")
            # valid_validator_hotkey = hotkey in valid_validator_hotkeys
            # Allow all validator hotkeys for data retrieval only
            # if verify_run(id,name, hotkey, allocated_keys) and allocated_keys and valid_validator_hotkey:
            allocated_keys_list.extend(allocated_keys)  # Add the keys to the list
        except Exception as e:
            bt.logging.error(f"API: Run ID: {run.id}, Name: {run.name}, Error: {e}")
    bt.logging.info("API: List allocated hotkeys is success")
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            "success": True,
            "message": "List allocated hotkeys",
            "data": jsonable_encoder(allocated_keys_list),
        },
    )


async def get_wandb_running_miners(
    config: Dict[str, Any], wandb: ComputeWandb, metagraph: Metagraph
):
    """
    Get the running miners from wandb_paginate_list
    """
    filter_rule = {
        "$and": [
            {"config.config.netuid": config.netuid},
            {"config.role": "miner"},
            {"state": "running"},
        ]
    }
    try:
        specs_details = {}
        running_hotkey = []
        runs = await run_in_threadpool(
            wandb.api.runs,
            f"{PUBLIC_WANDB_ENTITY}/{PUBLIC_WANDB_NAME}",
            filter_rule,
        )
        penalized_hotkeys = await run_in_threadpool(
            wandb.get_penalized_hotkeys_checklist, [], False
        )
        # bt.logging.info(penalized_hotkeys)
        for run in runs:
            run_config = run.config
            run_hotkey = run_config.get("hotkey")
            specs = run_config.get("specs")
            configs = run_config.get("config")
            # is_active = any(axon.hotkey == run_hotkey for axon in metagraph.axons)

            hotkey_axon = False
            is_active = False
            for axon in metagraph.axons:
                if axon.hotkey == run_hotkey:
                    hotkey_axon = axon
                    is_active = True if hotkey_axon else False
                    specs['version'] = axon.version

            #if is_active:
                #bt.logging.info(f"DEBUG - This hotkey is active - {run_hotkey}")
            # check the signature
            is_penalized = run_hotkey in penalized_hotkeys
            if (
                    run_hotkey
                    and configs
                    and not is_penalized
                    and is_active
            ):
                # bt.logging.info(f"DEBUG - This hotkey is OK - {run_hotkey}")
                running_hotkey.append(run_hotkey)
                if specs:
                    specs_details[run_hotkey] = specs
                else:
                    specs_details[run_hotkey] = {}
        return specs_details, running_hotkey
    except Exception as e:
        bt.logging.error(
            f"API: An error occurred while retrieving runs from wandb: {e}"
        )
        return {}, []


def _miner_pog_ok(db: ComputeDb, hours: int, ss58_address: str) -> bool:
    try:
        cursor = db.get_cursor()
        cursor.execute(
            """
            SELECT MIN(created_at)
            FROM pog_stats
            WHERE hotkey = ?
            """,
            (ss58_address,)
        )

        oldest_timestamp = cursor.fetchone()[0]

        cursor.execute(
            """
            SELECT COUNT(*)
            FROM pog_stats
            WHERE hotkey = ?
            AND gpu_name IS NOT NULL
            AND num_gpus IS NOT NULL
            """,
            (ss58_address,)
        )

        non_null_count = cursor.fetchone()[0]
        if oldest_timestamp and non_null_count > 0:
            if (datetime.now() - datetime.fromisoformat(oldest_timestamp)).total_seconds() <= hours * 3600:
                bt.logging.info(f"Hotkey not old enough: {ss58_address}")
                return False
            if ss58_address in MINER_BLACKLIST:
                bt.logging.warning(f"Blacklisted hotkey: {ss58_address}")
                return False
            return True
        return False
    except Exception as e:
        bt.logging.warning(f"API: POG record checking error occurred: {e} with hotkey {ss58_address}")
        return False
    finally:
        cursor.close()


# def _miner_is_older_than(db: ComputeDb, hours: int, ss58_address: str) -> bool:
#     cursor = db.get_cursor()
#     try:
#         cursor.execute("SELECT MIN(created_at) FROM challenge_details WHERE ss58_address = ?", (ss58_address,))
#         oldest_timestamp = cursor.fetchone()[0]
#         if oldest_timestamp:
#             if (datetime.now() - datetime.fromisoformat(oldest_timestamp)).total_seconds() <= hours * 3600:
#                 print(f"Hotkey not old enough: {ss58_address}")
#                 return False
#             return True
#         return False
#     except Exception as e:
#         bt.logging.info(f"Error occurred: {e}")
#         return False
#     finally:
#         cursor.close()


async def list_associated_hotkeys(
    subtensor: ComputeSubnetSubtensor,
    wandb: ComputeWandb,
    config: Dict[str, Any],
    netuid: Optional[int] = None,
    coldkey: Optional[str] = None,
    block: Optional[int] = None,
    only_show_allocated: Optional[bool] = False,
) -> JSONResponse:
    """
    This function gets all associated hotkeys from all validators.
    Only relevant for validators.
    """
    try:
        result = subtensor.query_runtime_api(
            runtime_api="StakeInfoRuntimeApi",
            method="get_stake_info_for_coldkey",
            params=[coldkey],
            block=block,
        )

        if result is None:
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content={
                    "success": True,
                    "message": "List associated hotkeys",
                    "data": jsonable_encoder([]),
                },
            )

        hokeys = []
        netuid = netuid or config.netuid
        stakes = StakeInfo.list_from_dicts(result)  # type: ignore
        for stake in stakes:
            if stake.netuid == netuid:
                hokeys.append(stake.hotkey_ss58)

        if not hokeys or not only_show_allocated:
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content={
                    "success": True,
                    "message": "List associated hotkeys",
                    "data": jsonable_encoder(hokeys),
                },
            )

        # Get the allocated hotkeys from wandb
        allocated_hotkeys = await run_in_threadpool(
            wandb.get_allocated_hotkeys,
            TRUSTED_VALIDATORS_HOTKEYS,
            False,
        )
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "success": True,
                "message": "List allocated hotkeys",
                "data": jsonable_encoder(list(set(hokeys) & set(allocated_hotkeys))),
            },
        )
    except Exception as e:
        bt.logging.error(f"API: list_allocated_hotkeys error with {e.__repr__()}")
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content={
                "success": False,
                "message": "Error occurred while fetching allocated hotkey from wandb",
                "err_detail": e.__repr__(),
            },
        )
